#!/usr/bin/env python3
"""
Markdown to PDF Converter using borb
Converts the Norwegian employment contract markdown to a professional PDF
"""

import re
from pathlib import Path
from decimal import Decimal
from typing import List, Tuple

from borb.pdf import Document
from borb.pdf import Page
from borb.pdf import PDF
from borb.pdf import SingleColumnLayout
from borb.pdf import Paragraph
from borb.pdf import Heading
from borb.pdf import UnorderedList
from borb.pdf import OrderedList
from borb.pdf import Table
from borb.pdf import TableCell
from borb.pdf import FlexibleColumnWidthTable
from borb.pdf.canvas.font.simple_font.true_type_font import TrueTypeFont
from borb.pdf.canvas.color.color import HexColor
# from borb.pdf.canvas.layout.text.alignment import Alignment


class MarkdownToPDFConverter:
    """Converts markdown content to PDF using borb"""
    
    def __init__(self):
        self.document = Document()
        self.page = Page()
        self.document.add_page(self.page)
        self.layout = SingleColumnLayout(self.page)
        
        # Define colors and fonts
        self.primary_color = HexColor("#2c3e50")
        self.secondary_color = HexColor("#34495e")
        self.accent_color = HexColor("#3498db")
        self.text_color = HexColor("#2c3e50")
        
    def parse_markdown_file(self, file_path: str) -> str:
        """Read and return markdown content from file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def split_into_sections(self, content: str) -> List[Tuple[str, str]]:
        """Split markdown content into sections based on headers"""
        sections = []
        lines = content.split('\n')
        current_section = []
        current_title = ""
        
        for line in lines:
            # Check for headers
            if line.startswith('#'):
                # Save previous section if exists
                if current_section:
                    sections.append((current_title, '\n'.join(current_section)))
                    current_section = []
                
                # Extract title (remove # and clean up)
                current_title = re.sub(r'^#+\s*', '', line).strip()
                current_title = re.sub(r'\*\*([^*]+)\*\*', r'\1', current_title)  # Remove bold markers
            else:
                current_section.append(line)
        
        # Add the last section
        if current_section:
            sections.append((current_title, '\n'.join(current_section)))
        
        return sections
    
    def process_text(self, text: str) -> str:
        """Clean and process text content"""
        # Remove markdown formatting
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'`([^`]+)`', r'\1', text)        # Code
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # Links
        
        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = text.strip()
        
        return text
    
    def add_title_page(self, title: str):
        """Add a title page to the document"""
        # Main title
        self.layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=Decimal(24),
            font_color=self.primary_color,
            margin_top=Decimal(50),
            margin_bottom=Decimal(30)
        ))

        # Subtitle
        self.layout.add(Paragraph(
            "Ringerike Landskap AS",
            font="Helvetica",
            font_size=Decimal(16),
            font_color=self.secondary_color,
            margin_bottom=Decimal(20)
        ))

        # Date
        from datetime import datetime
        self.layout.add(Paragraph(
            f"Generert: {datetime.now().strftime('%d. %B %Y')}",
            font="Helvetica",
            font_size=Decimal(12),
            font_color=self.secondary_color,
            margin_bottom=Decimal(50)
        ))
    
    def add_section(self, title: str, content: str):
        """Add a section with title and content"""
        if not title or not content.strip():
            return
        
        # Determine header level based on title
        if any(keyword in title.lower() for keyword in ['introduksjon', 'konklusjon']):
            font_size = Decimal(18)
            margin_top = Decimal(30)
        elif title.startswith('Del '):
            font_size = Decimal(16)
            margin_top = Decimal(25)
        else:
            font_size = Decimal(14)
            margin_top = Decimal(20)
        
        # Add section title
        self.layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=font_size,
            font_color=self.primary_color,
            margin_top=margin_top,
            margin_bottom=Decimal(15)
        ))
        
        # Process content
        self.process_content(content)
    
    def process_content(self, content: str):
        """Process and add content to the document"""
        lines = content.split('\n')
        current_paragraph = []
        in_table = False
        table_rows = []
        
        for line in lines:
            line = line.strip()
            
            if not line:
                # Empty line - end current paragraph
                if current_paragraph:
                    self.add_paragraph('\n'.join(current_paragraph))
                    current_paragraph = []
                continue
            
            # Check for table
            if '|' in line and not line.startswith('<!--'):
                if not in_table:
                    # Start of table
                    if current_paragraph:
                        self.add_paragraph('\n'.join(current_paragraph))
                        current_paragraph = []
                    in_table = True
                    table_rows = []
                
                # Process table row
                if not line.startswith('|---') and not line.startswith('| :---'):
                    cells = [cell.strip() for cell in line.split('|')[1:-1]]
                    if cells:  # Only add non-empty rows
                        table_rows.append(cells)
            else:
                # Not a table line
                if in_table:
                    # End of table
                    self.add_table(table_rows)
                    in_table = False
                    table_rows = []
                
                # Check for list items
                if line.startswith('* ') or line.startswith('- '):
                    if current_paragraph:
                        self.add_paragraph('\n'.join(current_paragraph))
                        current_paragraph = []
                    # For now, add as regular paragraph (can be enhanced later)
                    self.add_paragraph(line[2:])
                elif line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ')):
                    if current_paragraph:
                        self.add_paragraph('\n'.join(current_paragraph))
                        current_paragraph = []
                    # For now, add as regular paragraph (can be enhanced later)
                    self.add_paragraph(line[3:])
                else:
                    # Regular content line
                    current_paragraph.append(line)
        
        # Add any remaining content
        if in_table and table_rows:
            self.add_table(table_rows)
        elif current_paragraph:
            self.add_paragraph('\n'.join(current_paragraph))
    
    def add_paragraph(self, text: str):
        """Add a paragraph to the document"""
        if not text.strip():
            return
        
        text = self.process_text(text)
        
        self.layout.add(Paragraph(
            text,
            font="Helvetica",
            font_size=Decimal(11),
            font_color=self.text_color,
            margin_bottom=Decimal(10)
        ))
    
    def add_table(self, rows: List[List[str]]):
        """Add a table to the document"""
        if not rows:
            return
        
        # Determine number of columns
        max_cols = max(len(row) for row in rows)
        
        # Create table
        table = FlexibleColumnWidthTable(
            number_of_columns=max_cols,
            number_of_rows=len(rows)
        )
        
        # Add rows
        for i, row in enumerate(rows):
            for j, cell_content in enumerate(row):
                if j < max_cols:
                    # Clean cell content
                    cell_text = self.process_text(cell_content)
                    
                    # Header row styling
                    if i == 0:
                        cell = TableCell(
                            Paragraph(
                                cell_text,
                                font="Helvetica-Bold",
                                font_size=Decimal(10),
                                font_color=HexColor("#ffffff")
                            ),
                            background_color=self.primary_color
                        )
                    else:
                        cell = TableCell(
                            Paragraph(
                                cell_text,
                                font="Helvetica",
                                font_size=Decimal(9),
                                font_color=self.text_color
                            )
                        )
                    
                    table.add(cell)
        
        self.layout.add(table)
        self.layout.add(Paragraph(" ", font_size=Decimal(8)))  # Add space after table
    
    def save_pdf(self, output_path: str):
        """Save the document as PDF"""
        with open(output_path, "wb") as pdf_file:
            PDF.dumps(pdf_file, self.document)


def main():
    """Main function to convert markdown to PDF"""
    # File paths
    markdown_file = "Norsk arbeidskontrakt for anleggsgartner_.md"
    output_file = "py/Arbeidskontrakt_Ringerike_Landskap.pdf"
    
    # Check if markdown file exists
    if not Path(markdown_file).exists():
        print(f"Error: Markdown file '{markdown_file}' not found!")
        return
    
    print("Starting conversion...")
    
    # Create converter
    converter = MarkdownToPDFConverter()
    
    # Read markdown content
    content = converter.parse_markdown_file(markdown_file)
    
    # Split into sections
    sections = converter.split_into_sections(content)
    
    # Add title page
    if sections:
        first_title = sections[0][0] if sections[0][0] else "Arbeidskontrakt"
        converter.add_title_page(first_title)
    
    # Process each section
    for title, section_content in sections:
        converter.add_section(title, section_content)
    
    # Save PDF
    converter.save_pdf(output_file)
    
    print(f"PDF successfully created: {output_file}")
    print(f"Processed {len(sections)} sections")


if __name__ == "__main__":
    main()
