#!/usr/bin/env python3
"""
Multi-Library PDF Parser
Tries multiple PDF libraries to parse the Norwegian employment contract
Handles encrypted/protected PDFs and extracts structure for borb recreation
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from decimal import Decimal

# Import multiple PDF libraries
try:
    from borb.pdf import Document, PDF as BorbPDF
    BORB_AVAILABLE = True
except ImportError:
    BORB_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class MultiLibraryPDFParser:
    """Parse PDFs using multiple libraries for maximum compatibility"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.extracted_data = {}
        self.successful_methods = []
        
    def parse_with_pdfplumber(self) -> Dict[str, Any]:
        """Parse PDF using pdfplumber (best for text extraction)"""
        if not PDFPLUMBER_AVAILABLE:
            return {"error": "pdfplumber not available"}
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                data = {
                    "method": "pdfplumber",
                    "total_pages": len(pdf.pages),
                    "pages": [],
                    "metadata": pdf.metadata or {}
                }
                
                for i, page in enumerate(pdf.pages):
                    page_data = {
                        "page_number": i + 1,
                        "text": page.extract_text() or "",
                        "tables": page.extract_tables() or [],
                        "dimensions": {
                            "width": float(page.width),
                            "height": float(page.height)
                        },
                        "objects": len(page.objects),
                        "chars": len(page.chars),
                        "lines": len(page.lines) if hasattr(page, 'lines') else 0
                    }
                    data["pages"].append(page_data)
                
                self.successful_methods.append("pdfplumber")
                return data
                
        except Exception as e:
            return {"error": f"pdfplumber failed: {e}"}
    
    def parse_with_pypdf2(self) -> Dict[str, Any]:
        """Parse PDF using PyPDF2 (good for metadata and basic text)"""
        if not PYPDF2_AVAILABLE:
            return {"error": "PyPDF2 not available"}
        
        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                data = {
                    "method": "PyPDF2",
                    "total_pages": len(reader.pages),
                    "pages": [],
                    "metadata": dict(reader.metadata) if reader.metadata else {},
                    "is_encrypted": reader.is_encrypted
                }
                
                # Try to decrypt if encrypted
                if reader.is_encrypted:
                    try:
                        reader.decrypt("")  # Try empty password
                        data["decryption_attempted"] = True
                        data["decryption_successful"] = True
                    except:
                        data["decryption_attempted"] = True
                        data["decryption_successful"] = False
                        return data
                
                for i, page in enumerate(reader.pages):
                    try:
                        text = page.extract_text()
                        page_data = {
                            "page_number": i + 1,
                            "text": text,
                            "text_length": len(text),
                            "mediabox": {
                                "width": float(page.mediabox.width),
                                "height": float(page.mediabox.height)
                            }
                        }
                        data["pages"].append(page_data)
                    except Exception as e:
                        data["pages"].append({
                            "page_number": i + 1,
                            "error": str(e)
                        })
                
                self.successful_methods.append("PyPDF2")
                return data
                
        except Exception as e:
            return {"error": f"PyPDF2 failed: {e}"}
    
    def parse_with_borb(self) -> Dict[str, Any]:
        """Parse PDF using borb (best for recreation)"""
        if not BORB_AVAILABLE:
            return {"error": "borb not available"}
        
        try:
            with open(self.pdf_path, "rb") as pdf_file:
                document = BorbPDF.loads(pdf_file)
            
            data = {
                "method": "borb",
                "pages": [],
                "document_loaded": True
            }
            
            # Try to access pages
            page_count = 0
            try:
                while True:
                    try:
                        page = document.get_page(page_count)
                        page_data = {
                            "page_number": page_count + 1,
                            "accessible": True
                        }
                        data["pages"].append(page_data)
                        page_count += 1
                    except:
                        break
            except:
                pass
            
            data["total_pages"] = page_count
            self.successful_methods.append("borb")
            return data
            
        except Exception as e:
            return {"error": f"borb failed: {e}"}
    
    def analyze_pdf_structure(self) -> Dict[str, Any]:
        """Analyze PDF structure using all available methods"""
        results = {
            "file_info": {
                "path": self.pdf_path,
                "size": Path(self.pdf_path).stat().st_size,
                "exists": Path(self.pdf_path).exists()
            },
            "parsing_results": {},
            "best_result": None,
            "summary": {}
        }
        
        if not results["file_info"]["exists"]:
            results["error"] = "File not found"
            return results
        
        print(f"Analyzing PDF: {self.pdf_path}")
        print(f"File size: {results['file_info']['size']:,} bytes")
        
        # Try pdfplumber first (usually most reliable)
        print("\n--- Trying pdfplumber ---")
        pdfplumber_result = self.parse_with_pdfplumber()
        results["parsing_results"]["pdfplumber"] = pdfplumber_result
        
        if "error" not in pdfplumber_result:
            print(f"✓ pdfplumber: {pdfplumber_result['total_pages']} pages")
            results["best_result"] = pdfplumber_result
        else:
            print(f"✗ pdfplumber: {pdfplumber_result['error']}")
        
        # Try PyPDF2
        print("\n--- Trying PyPDF2 ---")
        pypdf2_result = self.parse_with_pypdf2()
        results["parsing_results"]["PyPDF2"] = pypdf2_result
        
        if "error" not in pypdf2_result:
            print(f"✓ PyPDF2: {pypdf2_result['total_pages']} pages")
            if not results["best_result"]:
                results["best_result"] = pypdf2_result
        else:
            print(f"✗ PyPDF2: {pypdf2_result['error']}")
        
        # Try borb
        print("\n--- Trying borb ---")
        borb_result = self.parse_with_borb()
        results["parsing_results"]["borb"] = borb_result
        
        if "error" not in borb_result:
            print(f"✓ borb: {borb_result['total_pages']} pages")
            if not results["best_result"]:
                results["best_result"] = borb_result
        else:
            print(f"✗ borb: {borb_result['error']}")
        
        # Create summary
        results["summary"] = {
            "successful_methods": self.successful_methods,
            "total_methods_tried": 3,
            "best_method": results["best_result"]["method"] if results["best_result"] else None,
            "parsing_successful": len(self.successful_methods) > 0
        }
        
        return results
    
    def extract_contract_structure(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Extract employment contract structure from the best parsing result"""
        if not analysis_results.get("best_result"):
            return {"error": "No successful parsing results"}
        
        best_result = analysis_results["best_result"]
        method = best_result["method"]
        
        structure = {
            "source_method": method,
            "document_info": {
                "total_pages": best_result.get("total_pages", 0),
                "metadata": best_result.get("metadata", {})
            },
            "content_analysis": {},
            "sections_identified": [],
            "form_fields": [],
            "tables": []
        }
        
        # Analyze content based on method used
        if method == "pdfplumber" and "pages" in best_result:
            structure["content_analysis"] = self._analyze_pdfplumber_content(best_result["pages"])
        elif method == "PyPDF2" and "pages" in best_result:
            structure["content_analysis"] = self._analyze_pypdf2_content(best_result["pages"])
        
        return structure
    
    def _analyze_pdfplumber_content(self, pages: List[Dict]) -> Dict[str, Any]:
        """Analyze content extracted with pdfplumber"""
        analysis = {
            "total_text_length": 0,
            "pages_with_text": 0,
            "pages_with_tables": 0,
            "common_words": {},
            "potential_sections": [],
            "text_samples": []
        }
        
        for page in pages:
            text = page.get("text", "")
            if text:
                analysis["total_text_length"] += len(text)
                analysis["pages_with_text"] += 1
                
                # Look for section headers (lines that might be titles)
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and (line.isupper() or 
                               any(keyword in line.lower() for keyword in 
                                   ['arbeidsavtale', 'arbeidsgiver', 'arbeidstaker', 'lønn', 'ferie', 'oppsigelse'])):
                        analysis["potential_sections"].append(line)
                
                # Store text sample
                if len(text) > 100:
                    analysis["text_samples"].append(text[:200] + "...")
            
            if page.get("tables"):
                analysis["pages_with_tables"] += 1
        
        return analysis
    
    def _analyze_pypdf2_content(self, pages: List[Dict]) -> Dict[str, Any]:
        """Analyze content extracted with PyPDF2"""
        analysis = {
            "total_text_length": 0,
            "pages_with_text": 0,
            "text_samples": []
        }
        
        for page in pages:
            text = page.get("text", "")
            if text:
                analysis["total_text_length"] += len(text)
                analysis["pages_with_text"] += 1
                
                if len(text) > 100:
                    analysis["text_samples"].append(text[:200] + "...")
        
        return analysis
    
    def generate_borb_recreation_code(self, structure: Dict[str, Any]) -> str:
        """Generate borb code to recreate the PDF structure"""
        
        total_pages = structure.get("document_info", {}).get("total_pages", 1)
        method = structure.get("source_method", "unknown")
        
        code = f'''#!/usr/bin/env python3
"""
Norwegian Employment Contract Recreation using borb
Generated from analysis of: {self.pdf_path}
Source parsing method: {method}
Total pages: {total_pages}
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor
from borb.pdf.canvas.layout.table import Table, TableCell, FlexibleColumnWidthTable

def create_norwegian_employment_contract():
    """Create a Norwegian employment contract based on the analyzed structure"""
    
    # Create document
    document = Document()
    
    # Styling
    black = HexColor("#000000")
    dark_blue = HexColor("#1f4e79")
    
    # Create {total_pages} page(s) based on original
    for page_num in range({total_pages}):
        page = Page()
        document.add_page(page)
        layout = SingleColumnLayout(page)
        
        if page_num == 0:
            # Main contract page
            layout.add(Paragraph(
                "ARBEIDSAVTALE",
                font="Helvetica-Bold",
                font_size=Decimal(18),
                font_color=black,
                margin_top=Decimal(30),
                margin_bottom=Decimal(25)
            ))
            
            # Standard Norwegian employment contract sections
            sections = [
                ("1. ARBEIDSGIVERS OPPLYSNINGER", [
                    "Bedriftens navn: ________________________________",
                    "Organisasjonsnummer: ____________________________",
                    "Adresse: _______________________________________",
                    "Poststed: ______________________________________"
                ]),
                
                ("2. ARBEIDSTAKERS OPPLYSNINGER", [
                    "Navn: _________________________________________",
                    "Fødselsnummer: _________________________________",
                    "Adresse: _____________________________________",
                    "Poststed: ____________________________________"
                ]),
                
                ("3. ARBEIDSFORHOLD", [
                    "Stillingstittel: _______________________________",
                    "Ansettelsesdato: _______________________________",
                    "Prøvetid: ____________________________________",
                    "Arbeidssted: __________________________________"
                ]),
                
                ("4. ARBEIDSTID", [
                    "Normal arbeidstid: _____ timer per uke",
                    "Arbeidstiden er fordelt på _____ dager per uke",
                    "Overtidsarbeid: _______________________________"
                ]),
                
                ("5. LØNN OG YTELSER", [
                    "Grunnlønn: ___________________________________",
                    "Utbetalingsdato: ______________________________",
                    "Andre ytelser: ________________________________"
                ])
            ]
            
            for section_title, section_items in sections:
                # Section header
                layout.add(Paragraph(
                    section_title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=dark_blue,
                    margin_top=Decimal(20),
                    margin_bottom=Decimal(8)
                ))
                
                # Section items
                for item in section_items:
                    layout.add(Paragraph(
                        item,
                        font="Helvetica",
                        font_size=Decimal(10),
                        font_color=black,
                        margin_bottom=Decimal(5)
                    ))
        
        else:
            # Additional pages
            layout.add(Paragraph(
                f"SIDE {{page_num + 1}}",
                font="Helvetica-Bold",
                font_size=Decimal(14),
                font_color=dark_blue,
                margin_top=Decimal(30),
                margin_bottom=Decimal(20)
            ))
            
            # Additional contract terms
            additional_sections = [
                ("6. FERIE", [
                    "Ferie reguleres av ferieloven",
                    "Feriepenger utbetales: ________________________"
                ]),
                
                ("7. OPPSIGELSE", [
                    "Oppsigelsestid fra arbeidsgiver: _______________",
                    "Oppsigelsestid fra arbeidstaker: _______________"
                ]),
                
                ("8. ØVRIGE BESTEMMELSER", [
                    "Taushetsplikt: ________________________________",
                    "Andre bestemmelser: ____________________________"
                ])
            ]
            
            for section_title, section_items in additional_sections:
                layout.add(Paragraph(
                    section_title,
                    font="Helvetica-Bold",
                    font_size=Decimal(12),
                    font_color=dark_blue,
                    margin_top=Decimal(15),
                    margin_bottom=Decimal(8)
                ))
                
                for item in section_items:
                    layout.add(Paragraph(
                        item,
                        font="Helvetica",
                        font_size=Decimal(10),
                        font_color=black,
                        margin_bottom=Decimal(5)
                    ))
    
    # Add signature section on last page
    layout.add(Paragraph(
        "UNDERSKRIFT",
        font="Helvetica-Bold",
        font_size=Decimal(14),
        font_color=dark_blue,
        margin_top=Decimal(40),
        margin_bottom=Decimal(20)
    ))
    
    # Signature table
    sig_table = FlexibleColumnWidthTable(number_of_columns=2, number_of_rows=3)
    
    sig_table.add(TableCell(Paragraph("ARBEIDSGIVER", font="Helvetica-Bold", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("ARBEIDSTAKER", font="Helvetica-Bold", font_size=Decimal(10))))
    
    sig_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("Dato: ________________", font="Helvetica", font_size=Decimal(10))))
    
    sig_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
    sig_table.add(TableCell(Paragraph("Underskrift: ________________", font="Helvetica", font_size=Decimal(10))))
    
    layout.add(sig_table)
    
    # Save document
    output_path = "recreated_norwegian_contract_from_analysis.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Norwegian employment contract recreated: {{output_path}}")
    return output_path

if __name__ == "__main__":
    create_norwegian_employment_contract()
'''
        
        return code


def main():
    """Main function to parse the Norwegian employment contract PDF"""
    pdf_file = "Norwegian - bokmal - Standard contract of employment-unlocked.pdf"
    
    if not Path(pdf_file).exists():
        print(f"Error: PDF file '{pdf_file}' not found!")
        return
    
    print("=== Multi-Library PDF Analysis ===")
    print(f"Available libraries:")
    print(f"  - borb: {BORB_AVAILABLE}")
    print(f"  - PyPDF2: {PYPDF2_AVAILABLE}")
    print(f"  - pdfplumber: {PDFPLUMBER_AVAILABLE}")
    
    # Create parser
    parser = MultiLibraryPDFParser(pdf_file)
    
    # Analyze with all available methods
    analysis_results = parser.analyze_pdf_structure()
    
    # Extract contract structure
    print(f"\n=== Extracting Contract Structure ===")
    structure = parser.extract_contract_structure(analysis_results)
    
    # Display results
    print(f"\n=== Results Summary ===")
    summary = analysis_results.get("summary", {})
    print(f"Successful methods: {summary.get('successful_methods', [])}")
    print(f"Best method: {summary.get('best_method', 'None')}")
    print(f"Parsing successful: {summary.get('parsing_successful', False)}")
    
    if analysis_results.get("best_result"):
        best = analysis_results["best_result"]
        print(f"Total pages: {best.get('total_pages', 'Unknown')}")
        
        if "pages" in best and best["pages"]:
            first_page = best["pages"][0]
            if "text" in first_page and first_page["text"]:
                print(f"Sample text from page 1:")
                sample = first_page["text"][:300] + "..." if len(first_page["text"]) > 300 else first_page["text"]
                print(f"  {sample}")
    
    # Save results and generate code
    print(f"\n=== Saving Results ===")
    
    # Save analysis results
    with open("py/pdf_analysis_multi_library.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
    
    # Generate and save recreation code
    recreation_code = parser.generate_borb_recreation_code(structure)
    with open("py/recreate_from_analysis.py", 'w', encoding='utf-8') as f:
        f.write(recreation_code)
    
    print("✓ Analysis results saved to: py/pdf_analysis_multi_library.json")
    print("✓ Recreation code saved to: py/recreate_from_analysis.py")
    
    print(f"\n=== Next Steps ===")
    print("1. Review the analysis results")
    print("2. Run: python py/recreate_from_analysis.py")
    print("3. Customize the recreation code for your specific needs")
    print("4. Use the structure to create dynamic contract generation")


if __name__ == "__main__":
    main()
