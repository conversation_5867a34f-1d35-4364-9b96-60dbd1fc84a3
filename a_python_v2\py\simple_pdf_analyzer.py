#!/usr/bin/env python3
"""
Simple PDF Analyzer using borb
Demonstrates basic PDF reading capabilities
"""

from pathlib import Path
from typing import Dict, Any

from borb.pdf import Document
from borb.pdf import PDF


class SimplePDFAnalyzer:
    """Simple PDF analyzer using borb"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.document = None
        self.load_document()
    
    def load_document(self):
        """Load the PDF document"""
        if not Path(self.pdf_path).exists():
            raise FileNotFoundError(f"PDF file not found: {self.pdf_path}")
        
        with open(self.pdf_path, "rb") as pdf_file:
            self.document = PDF.loads(pdf_file)
    
    def get_basic_info(self) -> Dict[str, Any]:
        """Get basic document information"""
        if not self.document:
            return {}
        
        file_size = Path(self.pdf_path).stat().st_size
        
        info = {
            "file_path": self.pdf_path,
            "file_size_bytes": file_size,
            "file_size_mb": round(file_size / (1024 * 1024), 2),
            "document_loaded": True
        }
        
        # Try to get page count
        try:
            page = self.document.get_page(0)
            info["has_content"] = True
            info["first_page_accessible"] = True
        except Exception as e:
            info["has_content"] = False
            info["first_page_accessible"] = False
            info["error"] = str(e)
        
        return info
    
    def demonstrate_pdf_creation_code(self) -> str:
        """Generate example code for creating similar PDFs"""
        code_template = '''#!/usr/bin/env python3
"""
PDF Creation Example using borb
Based on analysis of: {pdf_path}
"""

from decimal import Decimal
from borb.pdf import Document, Page, PDF, SingleColumnLayout, Paragraph
from borb.pdf.canvas.color.color import HexColor

def create_employment_contract():
    """Create an employment contract PDF"""
    # Create new document
    document = Document()
    page = Page()
    document.add_page(page)
    layout = SingleColumnLayout(page)
    
    # Define styling
    primary_color = HexColor("#2c3e50")
    text_color = HexColor("#2c3e50")
    
    # Title
    layout.add(Paragraph(
        "ARBEIDSKONTRAKT",
        font="Helvetica-Bold",
        font_size=Decimal(20),
        font_color=primary_color,
        margin_top=Decimal(20),
        margin_bottom=Decimal(20)
    ))
    
    # Company info
    layout.add(Paragraph(
        "Ringerike Landskap AS",
        font="Helvetica-Bold",
        font_size=Decimal(14),
        font_color=primary_color,
        margin_bottom=Decimal(10)
    ))
    
    # Contract sections
    sections = [
        ("1. ARBEIDSGIVERS OPPLYSNINGER", "Ringerike Landskap AS\\nOrg.nr: [ORGANISASJONSNUMMER]"),
        ("2. ARBEIDSTAKERS OPPLYSNINGER", "Navn: [NAVN]\\nFødselsnummer: [FØDSELSNUMMER]"),
        ("3. ARBEIDSFORHOLD", "Stillingstittel: Anleggsgartner\\nAnsettelsesdato: [DATO]"),
        ("4. ARBEIDSSTED", "Arbeidsstedet er: [ADRESSE]"),
        ("5. ARBEIDSTID", "Normal arbeidstid er 37,5 timer per uke"),
        ("6. LØNN", "Lønn utbetales månedlig"),
        ("7. FERIE", "Ferie reguleres av ferieloven"),
        ("8. OPPSIGELSE", "Oppsigelsestid i henhold til arbeidsmiljøloven")
    ]
    
    for title, content in sections:
        # Section title
        layout.add(Paragraph(
            title,
            font="Helvetica-Bold",
            font_size=Decimal(12),
            font_color=primary_color,
            margin_top=Decimal(15),
            margin_bottom=Decimal(5)
        ))
        
        # Section content
        layout.add(Paragraph(
            content,
            font="Helvetica",
            font_size=Decimal(11),
            font_color=text_color,
            margin_bottom=Decimal(10)
        ))
    
    # Signature section
    layout.add(Paragraph(
        "UNDERSKRIFT",
        font="Helvetica-Bold",
        font_size=Decimal(12),
        font_color=primary_color,
        margin_top=Decimal(30),
        margin_bottom=Decimal(20)
    ))
    
    layout.add(Paragraph(
        "Dato: ________________    Arbeidsgiver: ________________",
        font="Helvetica",
        font_size=Decimal(11),
        margin_bottom=Decimal(15)
    ))
    
    layout.add(Paragraph(
        "Dato: ________________    Arbeidstaker: ________________",
        font="Helvetica",
        font_size=Decimal(11),
        margin_bottom=Decimal(20)
    ))
    
    # Save the document
    output_path = "generated_employment_contract.pdf"
    with open(output_path, "wb") as pdf_file:
        PDF.dumps(pdf_file, document)
    
    print(f"Employment contract created: {{output_path}}")
    return output_path

if __name__ == "__main__":
    create_employment_contract()
'''.format(pdf_path=self.pdf_path)
        
        return code_template


def main():
    """Main function to demonstrate PDF analysis"""
    pdf_file = "py/Arbeidskontrakt_Ringerike_Landskap.pdf"
    
    if not Path(pdf_file).exists():
        print(f"Error: PDF file '{pdf_file}' not found!")
        print("Please run the simple_markdown_to_pdf.py script first.")
        return
    
    print("=== PDF Analysis with borb ===")
    print(f"Analyzing: {pdf_file}")
    
    try:
        # Create analyzer
        analyzer = SimplePDFAnalyzer(pdf_file)
        
        # Get basic information
        info = analyzer.get_basic_info()
        
        print("\\n=== Document Information ===")
        for key, value in info.items():
            print(f"{key}: {value}")
        
        # Generate example code
        example_code = analyzer.demonstrate_pdf_creation_code()
        
        # Save example code
        code_file = "py/employment_contract_generator.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(example_code)
        
        print(f"\\n=== Code Generation ===")
        print(f"Example code saved to: {code_file}")
        print("This code shows how to create similar PDFs programmatically.")
        
        print("\\n=== Summary ===")
        print("✓ Successfully loaded PDF with borb")
        print("✓ Extracted basic document information")
        print("✓ Generated example code for PDF creation")
        print("\\nborb is working correctly in your environment!")
        
        print("\\n=== Next Steps ===")
        print("1. Run the generated example: python py/employment_contract_generator.py")
        print("2. Modify the example to match your specific requirements")
        print("3. Use borb to read existing PDFs and extract their structure")
        print("4. Create dynamic PDF generation based on data inputs")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure the PDF file exists")
        print("2. Check that borb is properly installed")
        print("3. Verify the PDF is not corrupted")


if __name__ == "__main__":
    main()
